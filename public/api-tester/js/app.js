/**
 * 主应用逻辑
 * 协调各个模块，处理用户交互
 */

class ApiTesterApp {
  constructor() {
    this.apiInfo = null;
    this.currentRequest = null;
    this.streamController = null;
    this.enhancedStreamClient = null;
    this.streamMonitor = null;
    this.streamExporter = null;
    this.streamFilter = null;
    this.templates = {};
    this.init();
  }

  /**
   * 初始化应用
   */
  async init() {
    console.log('🚀 API测试工具初始化中...');

    try {
      // 加载历史记录
      apiClient.loadHistory();

      // 初始化事件监听器
      this.initEventListeners();

      // 初始化增强流式客户端
      this.initEnhancedStreamClient();

      // 初始化流式监控器
      this.initStreamMonitor();

      // 初始化流式导出器
      this.initStreamExporter();

      // 初始化流式过滤器
      this.initStreamFilter();

      // 加载API信息
      await this.loadApiInfo();

      // 渲染API列表
      this.renderApiList();

      // 渲染历史记录
      this.renderHistory();

      // 加载模板
      await this.loadTemplates();

      console.log('✅ API测试工具初始化完成');
      uiComponents.showNotification('API测试工具已就绪', 'success');

    } catch (error) {
      console.error('❌ 初始化失败:', error);
      uiComponents.showNotification('初始化失败: ' + error.message, 'error');
    }
  }

  /**
   * 初始化事件监听器
   */
  initEventListeners() {
    // 发送请求按钮
    const sendRequestBtn = document.getElementById('send-request');
    if (sendRequestBtn) {
      sendRequestBtn.addEventListener('click', () => this.sendRequest());
    }

    // 刷新API列表
    const refreshApisBtn = document.getElementById('refresh-apis');
    if (refreshApisBtn) {
      refreshApisBtn.addEventListener('click', () => this.loadApiInfo());
    }

    // 清除历史记录
    const clearHistoryBtn = document.getElementById('clear-history');
    if (clearHistoryBtn) {
      clearHistoryBtn.addEventListener('click', () => this.clearHistory());
    }

    // 导出配置
    const exportConfigBtn = document.getElementById('export-config');
    if (exportConfigBtn) {
      exportConfigBtn.addEventListener('click', () => this.exportConfig());
    }

    // 导入配置
    const importConfigBtn = document.getElementById('import-config-btn');
    const importConfigInput = document.getElementById('import-config');
    if (importConfigBtn && importConfigInput) {
      importConfigBtn.addEventListener('click', () => importConfigInput.click());
      importConfigInput.addEventListener('change', (e) => this.importConfig(e));
    }

    // 清空请求
    const clearRequestBtn = document.getElementById('clear-request');
    if (clearRequestBtn) {
      clearRequestBtn.addEventListener('click', () => this.clearRequest());
    }

    // 格式化JSON
    const formatJsonBtn = document.getElementById('format-json');
    if (formatJsonBtn) {
      formatJsonBtn.addEventListener('click', () => this.formatRequestBody());
    }

    // 复制响应
    const copyResponseBtn = document.getElementById('copy-response');
    if (copyResponseBtn) {
      copyResponseBtn.addEventListener('click', () => this.copyResponse());
    }

    // 下载响应
    const downloadResponseBtn = document.getElementById('download-response');
    if (downloadResponseBtn) {
      downloadResponseBtn.addEventListener('click', () => this.downloadResponse());
    }

    // 清空响应
    const clearResponseBtn = document.getElementById('clear-response');
    if (clearResponseBtn) {
      clearResponseBtn.addEventListener('click', () => this.clearResponse());
    }

    // 流式请求控制
    const startStreamBtn = document.getElementById('start-stream');
    const stopStreamBtn = document.getElementById('stop-stream');
    const clearStreamBtn = document.getElementById('clear-stream');
    
    if (startStreamBtn) {
      startStreamBtn.addEventListener('click', () => this.startStreamRequest());
    }
    if (stopStreamBtn) {
      stopStreamBtn.addEventListener('click', () => this.stopStreamRequest());
    }
    if (clearStreamBtn) {
      clearStreamBtn.addEventListener('click', () => this.clearStreamOutput());
    }

    // API搜索
    const apiSearchInput = document.getElementById('api-search');
    if (apiSearchInput) {
      apiSearchInput.addEventListener('input', Utils.debounce((e) => {
        this.filterApiList(e.target.value);
      }, 300));
    }

    // 请求体类型变化
    const bodyTypeSelect = document.getElementById('body-type');
    if (bodyTypeSelect) {
      bodyTypeSelect.addEventListener('change', () => this.updateBodyEditor());
    }

    // 认证类型变化
    const authTypeSelect = document.getElementById('auth-type');
    if (authTypeSelect) {
      authTypeSelect.addEventListener('change', () => this.updateAuthFields());
    }

    // 响应格式变化
    const responseFormatSelect = document.getElementById('response-format');
    if (responseFormatSelect) {
      responseFormatSelect.addEventListener('change', () => this.updateResponseFormat());
    }

    // 键盘快捷键
    document.addEventListener('keydown', (e) => {
      // Ctrl+Enter 发送请求
      if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
        e.preventDefault();
        this.sendRequest();
      }
      // Ctrl+K 聚焦搜索框
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        const searchInput = document.getElementById('api-search');
        if (searchInput) searchInput.focus();
      }
    });
  }

  /**
   * 加载API信息
   */
  async loadApiInfo() {
    try {
      uiComponents.showLoading('正在加载API信息...');
      this.apiInfo = await apiClient.getApiInfo();
      console.log('API信息加载成功:', this.apiInfo);
      this.renderApiList();
    } catch (error) {
      console.error('加载API信息失败:', error);
      uiComponents.showNotification('加载API信息失败: ' + error.message, 'error');
    } finally {
      uiComponents.hideLoading();
    }
  }

  /**
   * 渲染API列表
   */
  renderApiList() {
    const apiGroupsContainer = document.getElementById('api-groups');
    if (!apiGroupsContainer || !this.apiInfo) return;

    const endpoints = this.apiInfo.data?.endpoints || {};
    
    apiGroupsContainer.innerHTML = '';

    Object.entries(endpoints).forEach(([groupName, apis]) => {
      const groupElement = this.createApiGroup(groupName, apis);
      apiGroupsContainer.appendChild(groupElement);
    });
  }

  /**
   * 创建API分组元素
   * @param {string} groupName - 分组名称
   * @param {object} apis - API列表
   * @returns {HTMLElement} 分组元素
   */
  createApiGroup(groupName, apis) {
    const groupDiv = document.createElement('div');
    groupDiv.className = 'api-group';
    
    const groupHeader = document.createElement('div');
    groupHeader.className = 'api-group-header';
    groupHeader.innerHTML = `
      <span class="api-group-title">${this.getGroupDisplayName(groupName)}</span>
      <span class="api-group-toggle">▼</span>
    `;
    
    const apiList = document.createElement('div');
    apiList.className = 'api-list';
    
    Object.entries(apis).forEach(([endpoint, description]) => {
      const [method, path] = endpoint.split(' ');
      const apiItem = this.createApiItem(method, path, description);
      apiList.appendChild(apiItem);
    });
    
    // 添加折叠功能
    groupHeader.addEventListener('click', () => {
      groupDiv.classList.toggle('collapsed');
    });
    
    groupDiv.appendChild(groupHeader);
    groupDiv.appendChild(apiList);
    
    return groupDiv;
  }

  /**
   * 创建API项目元素
   * @param {string} method - HTTP方法
   * @param {string} path - API路径
   * @param {string} description - 描述
   * @returns {HTMLElement} API项目元素
   */
  createApiItem(method, path, description) {
    const itemDiv = document.createElement('div');
    itemDiv.className = 'api-item';
    itemDiv.innerHTML = `
      <span class="api-method ${Utils.getMethodClass(method)}">${method}</span>
      <span class="api-path">${path}</span>
    `;
    
    // 添加点击事件
    itemDiv.addEventListener('click', () => {
      this.selectApi(method, path, description);
    });
    
    // 添加工具提示
    itemDiv.title = description;
    
    return itemDiv;
  }

  /**
   * 选择API
   * @param {string} method - HTTP方法
   * @param {string} path - API路径
   * @param {string} description - 描述
   */
  selectApi(method, path, description) {
    // 更新UI状态
    document.querySelectorAll('.api-item').forEach(item => {
      item.classList.remove('active');
    });
    event.currentTarget.classList.add('active');
    
    // 设置请求信息
    const methodSelect = document.getElementById('request-method');
    const urlInput = document.getElementById('request-url');
    
    if (methodSelect) methodSelect.value = method;
    if (urlInput) {
      const baseUrl = urlInput.value.split('/api')[0] || 'http://localhost:3000';
      urlInput.value = baseUrl + path;
    }
    
    // 加载对应的模板
    this.loadApiTemplate(method, path);
    
    uiComponents.showNotification(`已选择 ${method} ${path}`, 'info', 2000);
  }

  /**
   * 获取分组显示名称
   * @param {string} groupName - 分组名称
   * @returns {string} 显示名称
   */
  getGroupDisplayName(groupName) {
    const displayNames = {
      models: '🤖 模型管理',
      chat: '💬 聊天对话',
      text: '✍️ 文本生成',
      system: '⚙️ 系统接口'
    };
    return displayNames[groupName] || groupName;
  }

  /**
   * 过滤API列表
   * @param {string} searchTerm - 搜索词
   */
  filterApiList(searchTerm) {
    const apiItems = document.querySelectorAll('.api-item');
    const searchLower = searchTerm.toLowerCase();
    
    apiItems.forEach(item => {
      const method = item.querySelector('.api-method')?.textContent || '';
      const path = item.querySelector('.api-path')?.textContent || '';
      const isMatch = method.toLowerCase().includes(searchLower) || 
                     path.toLowerCase().includes(searchLower);
      
      item.style.display = isMatch ? 'flex' : 'none';
    });
    
    // 隐藏空的分组
    const apiGroups = document.querySelectorAll('.api-group');
    apiGroups.forEach(group => {
      const visibleItems = group.querySelectorAll('.api-item[style*="flex"]');
      group.style.display = visibleItems.length > 0 ? 'block' : 'none';
    });
  }

  /**
   * 发送请求
   */
  async sendRequest() {
    try {
      const requestData = this.buildRequestData();
      
      uiComponents.showLoading('正在发送请求...');
      
      const result = await apiClient.sendRequest(requestData);
      
      this.displayResponse(result);
      this.renderHistory();
      
      uiComponents.showNotification('请求发送成功', 'success', 2000);
      
    } catch (error) {
      console.error('请求发送失败:', error);
      this.displayError(error);
      uiComponents.showNotification('请求发送失败: ' + error.message, 'error');
    } finally {
      uiComponents.hideLoading();
    }
  }

  /**
   * 构建请求数据
   * @returns {object} 请求数据
   */
  buildRequestData() {
    const method = document.getElementById('request-method')?.value || 'GET';
    const url = document.getElementById('request-url')?.value || '';
    const headers = uiComponents.getHeaders();
    const params = uiComponents.getParams();
    
    let body = null;
    const bodyType = document.getElementById('body-type')?.value;
    const bodyContent = document.getElementById('request-body')?.value;
    
    if (bodyType !== 'none' && bodyContent) {
      if (bodyType === 'json') {
        try {
          body = JSON.parse(bodyContent);
        } catch (error) {
          throw new Error('请求体JSON格式错误: ' + error.message);
        }
      } else {
        body = bodyContent;
      }
    }
    
    return { method, url, headers, params, body };
  }

  /**
   * 显示响应结果
   * @param {object} result - 响应结果
   */
  displayResponse(result) {
    const { response } = result;

    console.log('显示响应结果:', result);

    // 显示响应状态
    this.updateResponseStatus(response);

    // 显示响应体
    this.updateResponseBody(response.body);

    // 显示响应头
    this.updateResponseHeaders(response.headers);

    // 切换到响应体标签页
    const responseBodyTab = document.querySelector('.tab-header[data-tab="response-body"]');
    if (responseBodyTab) {
      console.log('切换到响应体标签页');
      uiComponents.switchTab(responseBodyTab);
    } else {
      console.warn('未找到响应体标签页');
    }
  }

  /**
   * 更新响应状态
   * @param {object} response - 响应对象
   */
  updateResponseStatus(response) {
    const statusSection = document.getElementById('response-status');
    const statusCode = document.getElementById('status-code');
    const statusText = document.getElementById('status-text');
    const responseTime = document.getElementById('response-time');
    const responseSize = document.getElementById('response-size');
    
    if (statusSection) statusSection.style.display = 'block';
    if (statusCode) {
      statusCode.textContent = response.status;
      statusCode.className = `status-code ${Utils.getStatusCodeClass(response.status)}`;
    }
    if (statusText) statusText.textContent = response.statusText;
    if (responseTime) responseTime.textContent = Utils.formatTime(response.time);
    if (responseSize) responseSize.textContent = Utils.formatFileSize(response.size);
  }

  /**
   * 更新响应体
   * @param {any} body - 响应体
   */
  updateResponseBody(body) {
    const responseBodyElement = document.getElementById('response-body');
    if (!responseBodyElement) {
      console.error('未找到响应体元素 #response-body');
      return;
    }

    console.log('更新响应体:', body);

    let content;
    if (typeof body === 'object') {
      content = Utils.highlightJSON(body);
    } else {
      content = Utils.escapeHtml(String(body));
    }

    responseBodyElement.innerHTML = content;
    console.log('响应体已更新');
  }

  /**
   * 更新响应头
   * @param {object} headers - 响应头
   */
  updateResponseHeaders(headers) {
    const responseHeadersElement = document.getElementById('response-headers');
    if (!responseHeadersElement) {
      console.error('未找到响应头元素 #response-headers');
      return;
    }

    console.log('更新响应头:', headers);

    const headersText = Object.entries(headers)
      .map(([key, value]) => `${key}: ${value}`)
      .join('\n');

    responseHeadersElement.textContent = headersText;
    console.log('响应头已更新');
  }

  /**
   * 显示错误信息
   * @param {object} error - 错误对象
   */
  displayError(error) {
    const responseBodyElement = document.getElementById('response-body');
    if (responseBodyElement) {
      responseBodyElement.innerHTML = `
        <div style="color: var(--danger-color); padding: 16px;">
          <h4>请求失败</h4>
          <p><strong>错误类型:</strong> ${error.error?.name || 'Unknown'}</p>
          <p><strong>错误信息:</strong> ${error.error?.message || error.message}</p>
          <p><strong>请求时间:</strong> ${Utils.formatTime(error.error?.time || 0)}</p>
        </div>
      `;
    }
    
    // 隐藏响应状态
    const statusSection = document.getElementById('response-status');
    if (statusSection) statusSection.style.display = 'none';
  }

  /**
   * 渲染历史记录
   */
  renderHistory() {
    const historyList = document.getElementById('history-list');
    if (!historyList) return;
    
    const history = apiClient.getHistory();
    
    historyList.innerHTML = '';
    
    if (history.length === 0) {
      historyList.innerHTML = '<div class="empty-state">暂无历史记录</div>';
      return;
    }
    
    history.slice(0, 10).forEach(item => {
      const historyItem = this.createHistoryItem(item);
      historyList.appendChild(historyItem);
    });
  }

  /**
   * 创建历史记录项
   * @param {object} item - 历史记录项
   * @returns {HTMLElement} 历史记录元素
   */
  createHistoryItem(item) {
    const itemDiv = document.createElement('div');
    itemDiv.className = 'history-item';
    
    const method = item.request.method;
    const url = item.request.url;
    const time = Utils.formatDateTime(item.request.timestamp);
    const status = item.response?.status || 'Error';
    
    itemDiv.innerHTML = `
      <span class="history-method ${Utils.getMethodClass(method)}">${method}</span>
      <span class="history-url" title="${url}">${this.shortenUrl(url)}</span>
      <span class="history-time">${time}</span>
    `;
    
    // 添加状态指示
    if (item.success) {
      itemDiv.style.borderLeft = '3px solid var(--success-color)';
    } else {
      itemDiv.style.borderLeft = '3px solid var(--danger-color)';
    }
    
    // 添加点击事件
    itemDiv.addEventListener('click', () => {
      this.loadHistoryItem(item);
    });
    
    return itemDiv;
  }

  /**
   * 缩短URL显示
   * @param {string} url - 完整URL
   * @returns {string} 缩短的URL
   */
  shortenUrl(url) {
    try {
      const urlObj = new URL(url);
      return urlObj.pathname + urlObj.search;
    } catch {
      return url.length > 30 ? url.substring(0, 30) + '...' : url;
    }
  }

  /**
   * 加载历史记录项
   * @param {object} item - 历史记录项
   */
  loadHistoryItem(item) {
    const { request, response } = item;
    
    // 设置请求信息
    const methodSelect = document.getElementById('request-method');
    const urlInput = document.getElementById('request-url');
    const bodyTextarea = document.getElementById('request-body');
    
    if (methodSelect) methodSelect.value = request.method;
    if (urlInput) urlInput.value = request.url;
    if (bodyTextarea && request.body) {
      bodyTextarea.value = typeof request.body === 'string' ? 
        request.body : JSON.stringify(request.body, null, 2);
    }
    
    // 设置请求头
    if (request.headers) {
      uiComponents.setHeaders(request.headers);
    }
    
    // 显示响应（如果有）
    if (response) {
      this.updateResponseStatus(response);
      this.updateResponseBody(response.body);
      this.updateResponseHeaders(response.headers);
    }
    
    uiComponents.showNotification('已加载历史记录', 'info', 2000);
  }

  /**
   * 清除历史记录
   */
  clearHistory() {
    uiComponents.showModal(
      '确认清除历史记录',
      '此操作将清除所有请求历史记录，且无法恢复。确定要继续吗？',
      {
        confirmText: '确认清除',
        onConfirm: () => {
          apiClient.clearHistory();
          this.renderHistory();
          uiComponents.showNotification('历史记录已清除', 'success');
        }
      }
    );
  }

  /**
   * 加载模板
   */
  async loadTemplates() {
    try {
      const response = await fetch('./templates/api-templates.json');
      if (response.ok) {
        this.templates = await response.json();
        console.log('模板加载成功:', this.templates);
      }
    } catch (error) {
      console.warn('模板加载失败:', error);
    }
  }

  /**
   * 加载API模板
   * @param {string} method - HTTP方法
   * @param {string} path - API路径
   */
  loadApiTemplate(method, path) {
    const templateKey = `${method.toLowerCase()}_${path.replace(/[^a-zA-Z0-9]/g, '_')}`;
    const template = this.templates[templateKey];

    if (template) {
      // 设置请求头
      if (template.headers) {
        uiComponents.setHeaders(template.headers);
      }

      // 设置URL参数
      if (template.params) {
        uiComponents.setParams(template.params);
      }

      // 设置请求体
      if (template.body) {
        const bodyTextarea = document.getElementById('request-body');
        const bodyTypeSelect = document.getElementById('body-type');

        if (bodyTextarea) {
          bodyTextarea.value = typeof template.body === 'string' ?
            template.body : JSON.stringify(template.body, null, 2);
        }

        if (bodyTypeSelect) {
          bodyTypeSelect.value = template.bodyType || 'json';
        }
      }

      console.log('已加载模板:', templateKey);
    }
  }

  /**
   * 格式化请求体
   */
  formatRequestBody() {
    const bodyTextarea = document.getElementById('request-body');
    const bodyType = document.getElementById('body-type')?.value;

    if (!bodyTextarea || bodyType !== 'json') return;

    try {
      const formatted = Utils.formatJSON(bodyTextarea.value);
      bodyTextarea.value = formatted;
      uiComponents.showNotification('JSON格式化成功', 'success', 2000);
    } catch (error) {
      uiComponents.showNotification('JSON格式化失败: ' + error.message, 'error');
    }
  }

  /**
   * 清空请求
   */
  clearRequest() {
    // 清空URL
    const urlInput = document.getElementById('request-url');
    if (urlInput) {
      const baseUrl = urlInput.value.split('/api')[0] || 'http://localhost:3000';
      urlInput.value = baseUrl;
    }

    // 重置方法
    const methodSelect = document.getElementById('request-method');
    if (methodSelect) methodSelect.value = 'GET';

    // 清空请求头
    uiComponents.clearHeaders();

    // 清空参数
    uiComponents.clearParams();

    // 清空请求体
    const bodyTextarea = document.getElementById('request-body');
    if (bodyTextarea) bodyTextarea.value = '';

    // 重置请求体类型
    const bodyTypeSelect = document.getElementById('body-type');
    if (bodyTypeSelect) bodyTypeSelect.value = 'none';

    // 重置认证
    const authTypeSelect = document.getElementById('auth-type');
    if (authTypeSelect) authTypeSelect.value = 'none';

    uiComponents.showNotification('请求已清空', 'info', 2000);
  }

  /**
   * 复制响应
   */
  async copyResponse() {
    const responseBody = document.getElementById('response-body');
    if (!responseBody) return;

    const text = responseBody.textContent || responseBody.innerText;
    const success = await Utils.copyToClipboard(text);

    if (success) {
      uiComponents.showNotification('响应已复制到剪贴板', 'success', 2000);
    } else {
      uiComponents.showNotification('复制失败', 'error');
    }
  }

  /**
   * 下载响应
   */
  downloadResponse() {
    const responseBody = document.getElementById('response-body');
    if (!responseBody) return;

    const text = responseBody.textContent || responseBody.innerText;
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `api-response-${timestamp}.json`;

    Utils.downloadTextFile(text, filename, 'application/json');
    uiComponents.showNotification('响应已下载', 'success', 2000);
  }

  /**
   * 清空响应
   */
  clearResponse() {
    const responseBody = document.getElementById('response-body');
    const responseHeaders = document.getElementById('response-headers');
    const statusSection = document.getElementById('response-status');

    if (responseBody) responseBody.innerHTML = '等待响应...';
    if (responseHeaders) responseHeaders.textContent = '等待响应...';
    if (statusSection) statusSection.style.display = 'none';

    uiComponents.showNotification('响应已清空', 'info', 2000);
  }

  /**
   * 初始化增强流式客户端
   */
  initEnhancedStreamClient() {
    this.enhancedStreamClient = new EnhancedStreamClient({
      connectionTimeout: 15000,
      responseTimeout: 60000,
      maxRetries: 5,
      retryDelay: 2000,
      enableMetrics: true,
      metricsInterval: 1000
    });

    console.log('🔄 增强流式客户端已初始化');
  }

  /**
   * 初始化流式监控器
   */
  initStreamMonitor() {
    // 创建监控器容器
    const streamViewer = document.querySelector('.stream-viewer');
    if (streamViewer) {
      // 在流式工具栏后添加监控器容器
      const toolbar = streamViewer.querySelector('.stream-toolbar');
      if (toolbar) {
        const monitorContainer = document.createElement('div');
        monitorContainer.id = 'stream-monitor-container';
        monitorContainer.className = 'stream-monitor-container';
        toolbar.insertAdjacentElement('afterend', monitorContainer);

        // 初始化监控器
        this.streamMonitor = new StreamMonitor('stream-monitor-container');
        console.log('📊 流式监控器已初始化');
      }
    }
  }

  /**
   * 初始化流式导出器
   */
  initStreamExporter() {
    this.streamExporter = new StreamExporter();
    console.log('📤 流式导出器已初始化');
  }

  /**
   * 初始化流式过滤器
   */
  initStreamFilter() {
    // 创建过滤器容器
    const streamViewer = document.querySelector('.stream-viewer');
    if (streamViewer) {
      // 在监控器后添加过滤器容器
      const monitorContainer = document.getElementById('stream-monitor-container');
      if (monitorContainer) {
        const filterContainer = document.createElement('div');
        filterContainer.id = 'stream-filter-container';
        filterContainer.className = 'stream-filter-container';
        monitorContainer.insertAdjacentElement('afterend', filterContainer);

        // 初始化过滤器
        this.streamFilter = new StreamFilter('stream-filter-container');
        console.log('🔍 流式过滤器已初始化');
      }
    }
  }

  /**
   * 开始流式请求
   */
  startStreamRequest() {
    try {
      const requestData = this.buildRequestData();

      // 确保启用流式模式
      requestData.params = { ...requestData.params, stream: true };

      // 清空之前的输出
      const streamMessages = document.getElementById('stream-messages');
      if (streamMessages) streamMessages.innerHTML = '';

      // 重置监控器
      if (this.streamMonitor) {
        this.streamMonitor.reset();
      }

      // 构建流式请求URL
      let streamUrl = this.getStreamUrl(requestData);

      // 使用增强流式客户端
      this.enhancedStreamClient.connect(streamUrl, requestData.headers, {
        onOpen: () => this.handleStreamOpen(),
        onMessage: (data, event) => this.handleStreamMessage(data, event),
        onError: (error) => this.handleStreamError(error),
        onClose: () => this.handleStreamComplete(),
        onStateChange: (newState, oldState) => this.handleStreamStateChange(newState, oldState),
        onMetrics: (metrics) => this.handleStreamMetrics(metrics)
      });

      uiComponents.showNotification('流式请求已开始', 'info', 2000);

    } catch (error) {
      console.error('启动流式请求失败:', error);
      uiComponents.showNotification('启动流式请求失败: ' + error.message, 'error');
    }
  }

  /**
   * 构建流式请求URL
   * @param {object} requestData - 请求数据
   * @returns {string} 流式请求URL
   */
  getStreamUrl(requestData) {
    let url = requestData.url;

    // 添加流式参数
    const params = new URLSearchParams(requestData.params);
    params.set('stream', 'true');

    // 如果是POST请求，需要特殊处理
    if (requestData.method.toUpperCase() === 'POST' && requestData.body) {
      // 对于POST请求，通常需要先发送请求体，然后建立SSE连接
      // 这里简化处理，直接在URL中添加参数
      url += (url.includes('?') ? '&' : '?') + params.toString();
    } else {
      url += (url.includes('?') ? '&' : '?') + params.toString();
    }

    return url;
  }

  /**
   * 处理流式连接打开
   */
  handleStreamOpen() {
    console.log('🔗 流式连接已建立');

    if (this.streamMonitor) {
      this.streamMonitor.updateConnectionState('connected');
    }

    uiComponents.showNotification('流式连接已建立', 'success', 2000);
  }

  /**
   * 处理流式状态变更
   * @param {string} newState - 新状态
   * @param {string} oldState - 旧状态
   */
  handleStreamStateChange(newState, oldState) {
    console.log(`🔄 流式连接状态变更: ${oldState} -> ${newState}`);

    if (this.streamMonitor) {
      this.streamMonitor.updateConnectionState(newState);
    }

    // 更新UI状态指示器
    const streamStatus = document.getElementById('stream-status');
    if (streamStatus) {
      const stateTexts = {
        'connected': '已连接',
        'connecting': '连接中...',
        'reconnecting': '重连中...',
        'disconnected': '已断开',
        'error': '连接错误'
      };

      streamStatus.textContent = stateTexts[newState] || newState;
      streamStatus.className = `stream-status ${newState}`;
    }
  }

  /**
   * 处理流式性能指标
   * @param {object} metrics - 性能指标
   */
  handleStreamMetrics(metrics) {
    if (this.streamMonitor) {
      this.streamMonitor.updateMetrics(metrics);
    }
  }

  /**
   * 停止流式请求
   */
  stopStreamRequest() {
    // 使用增强流式客户端断开连接
    if (this.enhancedStreamClient) {
      this.enhancedStreamClient.disconnect();
    }

    // 兼容旧的流式控制器
    if (this.streamController) {
      this.streamController();
      this.streamController = null;
    }

    // 兼容旧的API客户端
    apiClient.stopStreamRequest();

    uiComponents.showNotification('流式请求已停止', 'info', 2000);
  }

  /**
   * 清空流式输出
   */
  clearStreamOutput() {
    const streamMessages = document.getElementById('stream-messages');
    if (streamMessages) {
      streamMessages.innerHTML = '';
    }
  }

  /**
   * 处理流式消息
   * @param {any} data - 流式数据
   * @param {MessageEvent} event - 原始事件对象
   */
  handleStreamMessage(data, event) {
    const streamMessages = document.getElementById('stream-messages');
    const enableStyling = document.getElementById('enable-sse-styling')?.checked ?? true;

    if (!streamMessages) return null;

    // 记录消息到监控器
    if (this.streamMonitor) {
      this.streamMonitor.recordMessage({
        data: data,
        size: event ? event.data.length : JSON.stringify(data).length,
        timestamp: Date.now()
      });
    }

    let messageElement = null;

    // 如果启用样式区分，使用增强的消息渲染
    if (enableStyling && typeof data === 'object' && data.content_type) {
      messageElement = this.renderStyledSSEMessage(data, streamMessages);
    } else {
      // 回退到原始显示方式
      messageElement = this.renderPlainSSEMessage(data, streamMessages);
    }

    // 自动滚动到底部
    streamMessages.scrollTop = streamMessages.scrollHeight;

    return messageElement;
  }

  /**
   * 渲染样式化的SSE消息
   * @param {object} data - SSE消息数据
   * @param {HTMLElement} container - 容器元素
   */
  renderStyledSSEMessage(data, container) {
    const messageElement = document.createElement('div');
    messageElement.className = `sse-message ${this.getContentTypeClass(data.content_type)}`;

    // 创建消息头部
    const headerElement = document.createElement('div');
    headerElement.className = 'sse-message-header';

    // 添加图标
    const iconElement = document.createElement('span');
    iconElement.className = 'sse-message-icon';
    iconElement.textContent = this.getContentTypeIcon(data.content_type);

    // 添加类型标签
    const typeElement = document.createElement('span');
    typeElement.className = 'sse-message-type';
    typeElement.textContent = this.getContentTypeLabel(data.content_type);

    // 添加时间戳
    const timestampElement = document.createElement('span');
    timestampElement.className = 'sse-message-timestamp';
    timestampElement.textContent = this.formatTimestamp(data.created_at || Date.now());

    headerElement.appendChild(iconElement);
    headerElement.appendChild(typeElement);
    headerElement.appendChild(timestampElement);

    // 创建消息内容
    const contentElement = document.createElement('div');
    contentElement.className = 'sse-message-content';

    // 根据content_type处理内容显示
    if (data.content_type === 'card' && typeof data.content === 'string') {
      try {
        // 尝试格式化JSON内容
        const parsedContent = JSON.parse(data.content);
        contentElement.textContent = JSON.stringify(parsedContent, null, 2);
      } catch {
        contentElement.textContent = data.content;
      }
    } else {
      contentElement.textContent = data.content || JSON.stringify(data, null, 2);
    }

    messageElement.appendChild(headerElement);
    messageElement.appendChild(contentElement);
    container.appendChild(messageElement);
  }

  /**
   * 渲染普通的SSE消息（回退模式）
   * @param {any} data - 消息数据
   * @param {HTMLElement} container - 容器元素
   */
  renderPlainSSEMessage(data, container) {
    const messageElement = document.createElement('div');
    messageElement.className = 'sse-message unknown';

    const headerElement = document.createElement('div');
    headerElement.className = 'sse-message-header';

    const iconElement = document.createElement('span');
    iconElement.className = 'sse-message-icon';
    iconElement.textContent = '📄';

    const typeElement = document.createElement('span');
    typeElement.className = 'sse-message-type';
    typeElement.textContent = '原始数据';

    const timestampElement = document.createElement('span');
    timestampElement.className = 'sse-message-timestamp';
    timestampElement.textContent = this.formatTimestamp(Date.now());

    headerElement.appendChild(iconElement);
    headerElement.appendChild(typeElement);
    headerElement.appendChild(timestampElement);

    const contentElement = document.createElement('div');
    contentElement.className = 'sse-message-content';

    if (typeof data === 'object') {
      contentElement.textContent = JSON.stringify(data, null, 2);
    } else {
      contentElement.textContent = String(data);
    }

    messageElement.appendChild(headerElement);
    messageElement.appendChild(contentElement);
    container.appendChild(messageElement);
  }

  /**
   * 获取content_type对应的CSS类名
   * @param {string} contentType - 内容类型
   * @returns {string} CSS类名
   */
  getContentTypeClass(contentType) {
    const typeMap = {
      'progress': 'progress',    // 联网搜索进度
      'card': 'card',           // 搜索结果卡片
      'thinking': 'thinking',   // AI思考内容
      'text': 'text'           // 正式回答
    };
    return typeMap[contentType] || 'unknown';
  }

  /**
   * 获取content_type对应的图标
   * @param {string} contentType - 内容类型
   * @returns {string} 图标字符
   */
  getContentTypeIcon(contentType) {
    const iconMap = {
      'progress': '🔍',    // 联网搜索进度 - 搜索图标
      'card': '📋',        // 搜索结果卡片 - 卡片图标
      'thinking': '🤔',    // AI思考内容 - 思考图标
      'text': '💬'         // 正式回答 - 对话图标
    };
    return iconMap[contentType] || '📄';
  }

  /**
   * 获取content_type对应的中文标签
   * @param {string} contentType - 内容类型
   * @returns {string} 中文标签
   */
  getContentTypeLabel(contentType) {
    const labelMap = {
      'progress': '联网搜索',
      'card': '搜索结果',
      'thinking': '思考过程',
      'text': '正式回答'
    };
    return labelMap[contentType] || '未知类型';
  }

  /**
   * 格式化时间戳
   * @param {number} timestamp - 时间戳（毫秒或秒）
   * @returns {string} 格式化的时间字符串
   */
  formatTimestamp(timestamp) {
    // 处理秒级时间戳
    if (timestamp < 10000000000) {
      timestamp *= 1000;
    }

    const date = new Date(timestamp);
    const now = new Date();

    // 如果是今天，只显示时间
    if (date.toDateString() === now.toDateString()) {
      return date.toLocaleTimeString('zh-CN', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    }

    // 否则显示完整日期时间
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
  }

  /**
   * 处理流式错误
   * @param {object} error - 增强的错误对象
   */
  handleStreamError(error) {
    console.error('🚨 流式请求错误:', error);

    // 记录错误到监控器
    if (this.streamMonitor) {
      this.streamMonitor.recordError(error);
    }

    const streamMessages = document.getElementById('stream-messages');

    if (streamMessages) {
      // 创建增强的错误消息元素
      const errorData = {
        content_type: 'error',
        content: this.formatErrorMessage(error),
        created_at: Date.now(),
        error_type: error.type,
        retry_count: error.retryCount || 0
      };
      this.renderErrorMessage(errorData, streamMessages);
    }

    // 根据错误类型显示不同的通知
    const errorTypeMessages = {
      'network_error': '网络连接错误',
      'timeout_error': '连接超时',
      'parse_error': '数据解析错误',
      'connection_error': '连接建立失败',
      'server_error': '服务器错误',
      'unknown_error': '未知错误'
    };

    const errorMessage = errorTypeMessages[error.type] || '流式请求错误';
    uiComponents.showNotification(`${errorMessage}: ${error.message}`, 'error', 5000);
  }

  /**
   * 格式化错误消息
   * @param {object} error - 错误对象
   * @returns {string} 格式化的错误消息
   */
  formatErrorMessage(error) {
    let message = `[${error.type || 'UNKNOWN'}] ${error.message}`;

    if (error.retryCount > 0) {
      message += ` (重试次数: ${error.retryCount})`;
    }

    if (error.timestamp) {
      const time = new Date(error.timestamp).toLocaleTimeString();
      message += ` [${time}]`;
    }

    return message;
  }

  /**
   * 渲染错误消息
   * @param {object} errorData - 错误数据
   * @param {HTMLElement} container - 容器元素
   */
  renderErrorMessage(errorData, container) {
    const messageElement = document.createElement('div');
    messageElement.className = 'sse-message error';
    messageElement.style.borderLeftColor = 'var(--danger-color)';
    messageElement.style.backgroundColor = 'rgba(220, 53, 69, 0.05)';

    const headerElement = document.createElement('div');
    headerElement.className = 'sse-message-header';

    const iconElement = document.createElement('span');
    iconElement.className = 'sse-message-icon';
    iconElement.textContent = '❌';
    iconElement.style.color = 'var(--danger-color)';

    const typeElement = document.createElement('span');
    typeElement.className = 'sse-message-type';
    typeElement.textContent = '连接错误';
    typeElement.style.color = 'var(--danger-color)';

    const timestampElement = document.createElement('span');
    timestampElement.className = 'sse-message-timestamp';
    timestampElement.textContent = this.formatTimestamp(errorData.created_at);

    headerElement.appendChild(iconElement);
    headerElement.appendChild(typeElement);
    headerElement.appendChild(timestampElement);

    const contentElement = document.createElement('div');
    contentElement.className = 'sse-message-content';
    contentElement.textContent = errorData.content;
    contentElement.style.color = 'var(--danger-color)';

    messageElement.appendChild(headerElement);
    messageElement.appendChild(contentElement);
    container.appendChild(messageElement);
  }

  /**
   * 处理流式完成
   */
  handleStreamComplete() {
    const streamStatus = document.getElementById('stream-status');
    if (streamStatus) {
      streamStatus.textContent = '已完成';
      streamStatus.className = 'stream-status';
    }

    uiComponents.showNotification('流式请求已完成', 'success', 2000);
  }

  /**
   * 更新请求体编辑器
   */
  updateBodyEditor() {
    const bodyType = document.getElementById('body-type')?.value;
    const bodyTextarea = document.getElementById('request-body');
    const formatJsonBtn = document.getElementById('format-json');

    if (!bodyTextarea) return;

    switch (bodyType) {
      case 'none':
        bodyTextarea.style.display = 'none';
        if (formatJsonBtn) formatJsonBtn.style.display = 'none';
        break;
      case 'json':
        bodyTextarea.style.display = 'block';
        bodyTextarea.placeholder = '请输入JSON格式的请求体...';
        if (formatJsonBtn) formatJsonBtn.style.display = 'inline-flex';
        break;
      case 'text':
        bodyTextarea.style.display = 'block';
        bodyTextarea.placeholder = '请输入纯文本内容...';
        if (formatJsonBtn) formatJsonBtn.style.display = 'none';
        break;
      case 'xml':
        bodyTextarea.style.display = 'block';
        bodyTextarea.placeholder = '请输入XML格式的内容...';
        if (formatJsonBtn) formatJsonBtn.style.display = 'none';
        break;
      default:
        bodyTextarea.style.display = 'block';
        if (formatJsonBtn) formatJsonBtn.style.display = 'none';
    }
  }

  /**
   * 更新认证字段
   */
  updateAuthFields() {
    const authType = document.getElementById('auth-type')?.value;
    const authFields = document.getElementById('auth-fields');

    if (!authFields) return;

    authFields.innerHTML = '';

    switch (authType) {
      case 'bearer':
        authFields.innerHTML = `
          <div class="form-group">
            <label for="bearer-token">Bearer Token</label>
            <input type="text" id="bearer-token" class="form-control" placeholder="请输入Bearer Token">
          </div>
        `;
        break;
      case 'basic':
        authFields.innerHTML = `
          <div class="form-group">
            <label for="basic-username">用户名</label>
            <input type="text" id="basic-username" class="form-control" placeholder="请输入用户名">
          </div>
          <div class="form-group">
            <label for="basic-password">密码</label>
            <input type="password" id="basic-password" class="form-control" placeholder="请输入密码">
          </div>
        `;
        break;
      case 'api-key':
        authFields.innerHTML = `
          <div class="form-group">
            <label for="api-key-name">API Key 名称</label>
            <input type="text" id="api-key-name" class="form-control" placeholder="例如: X-API-Key" value="X-API-Key">
          </div>
          <div class="form-group">
            <label for="api-key-value">API Key 值</label>
            <input type="text" id="api-key-value" class="form-control" placeholder="请输入API Key">
          </div>
        `;
        break;
    }
  }

  /**
   * 更新响应格式
   */
  updateResponseFormat() {
    const format = document.getElementById('response-format')?.value;
    const responseBody = document.getElementById('response-body');

    if (!responseBody) return;

    // 这里可以根据格式重新渲染响应内容
    // 目前保持现有内容不变
  }

  /**
   * 导出配置
   */
  exportConfig() {
    const config = {
      version: '1.0.0',
      timestamp: Date.now(),
      request: {
        method: document.getElementById('request-method')?.value,
        url: document.getElementById('request-url')?.value,
        headers: uiComponents.getHeaders(),
        params: uiComponents.getParams(),
        body: document.getElementById('request-body')?.value,
        bodyType: document.getElementById('body-type')?.value,
        authType: document.getElementById('auth-type')?.value
      },
      history: apiClient.getHistory(),
      theme: uiComponents.currentTheme
    };

    const filename = `api-tester-config-${new Date().toISOString().split('T')[0]}.json`;
    Utils.downloadTextFile(JSON.stringify(config, null, 2), filename, 'application/json');

    uiComponents.showNotification('配置已导出', 'success');
  }

  /**
   * 导入配置
   * @param {Event} event - 文件选择事件
   */
  async importConfig(event) {
    const file = event.target.files[0];
    if (!file) return;

    try {
      const content = await Utils.readFileAsText(file);
      const config = JSON.parse(content);

      // 验证配置格式
      if (!config.version || !config.request) {
        throw new Error('无效的配置文件格式');
      }

      // 恢复请求配置
      const { request } = config;
      if (request.method) {
        const methodSelect = document.getElementById('request-method');
        if (methodSelect) methodSelect.value = request.method;
      }

      if (request.url) {
        const urlInput = document.getElementById('request-url');
        if (urlInput) urlInput.value = request.url;
      }

      if (request.headers) {
        uiComponents.setHeaders(request.headers);
      }

      if (request.params) {
        uiComponents.setParams(request.params);
      }

      if (request.body) {
        const bodyTextarea = document.getElementById('request-body');
        if (bodyTextarea) bodyTextarea.value = request.body;
      }

      if (request.bodyType) {
        const bodyTypeSelect = document.getElementById('body-type');
        if (bodyTypeSelect) bodyTypeSelect.value = request.bodyType;
        this.updateBodyEditor();
      }

      if (request.authType) {
        const authTypeSelect = document.getElementById('auth-type');
        if (authTypeSelect) authTypeSelect.value = request.authType;
        this.updateAuthFields();
      }

      // 恢复主题
      if (config.theme) {
        uiComponents.setTheme(config.theme);
      }

      uiComponents.showNotification('配置导入成功', 'success');

    } catch (error) {
      console.error('导入配置失败:', error);
      uiComponents.showNotification('导入配置失败: ' + error.message, 'error');
    }

    // 清空文件选择
    event.target.value = '';
  }
}

// 当页面加载完成时初始化应用
document.addEventListener('DOMContentLoaded', () => {
  // 确保所有依赖都已加载
  const checkDependencies = () => {
    if (typeof Utils !== 'undefined' &&
        typeof window.apiClient !== 'undefined' &&
        typeof window.uiComponents !== 'undefined') {
      console.log('所有依赖已加载，初始化应用...');
      window.app = new ApiTesterApp();
    } else {
      console.log('等待依赖加载...');
      setTimeout(checkDependencies, 100);
    }
  };

  checkDependencies();
});

// 导出应用类（如果支持模块化）
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ApiTesterApp;
}
